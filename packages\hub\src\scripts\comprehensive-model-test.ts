#!/usr/bin/env tsx

/**
 * Comprehensive Model Testing Script
 * Tests multiple models and scenarios to identify hang patterns
 */

import axios from 'axios';
import dotenv from 'dotenv';
import path from 'path';

// Load environment variables
dotenv.config({ path: path.join(process.cwd(), '.env.local') });

interface TestResult {
  model: string;
  scenario: string;
  success: boolean;
  duration: number;
  error?: string;
  responseLength?: number;
}

const TEST_SCENARIOS = {
  simple: {
    name: 'Simple Itinerary',
    content: `Day 1: Paris
- Morning: Visit Eiffel Tower
- Afternoon: Louvre Museum
- Evening: Seine River cruise`,
    systemPrompt: 'Parse this travel itinerary into JSON format with title, destination, and activities.'
  },
  
  complex: {
    name: 'Complex Multi-City',
    content: `Day 1-3: London
- Day 1: Arrive Heathrow, Tower Bridge, Borough Market
- Day 2: British Museum, Covent Garden, West End show
- Day 3: Windsor Castle day trip

Day 4-6: Paris  
- Day 4: Eurostar to Paris, Eiffel Tower, Champs-Élysées
- Day 5: Louvre, Notre-Dame, Latin Quarter
- Day 6: Versailles day trip

Day 7-9: Rome
- Day 7: Flight to Rome, Colosseum, Roman Forum
- Day 8: Vatican City, Sistine Chapel, St. Peter's
- Day 9: Trevi Fountain, Spanish Steps, departure`,
    systemPrompt: 'Parse this detailed multi-city travel itinerary into structured JSON format.'
  }
};

const MODELS_TO_TEST = [
  'moonshotai/kimi-k2:free',
  'google/gemini-2.5-flash', 
  'deepseek/deepseek-chat-v3-0324:free'
];

async function testModel(model: string, scenario: string, content: string, systemPrompt: string): Promise<TestResult> {
  const startTime = Date.now();
  
  try {
    console.log(`🧪 Testing ${model} with ${scenario}...`);
    
    const apiKey = process.env.OPENROUTER_API_KEY;
    if (!apiKey) {
      throw new Error('OpenRouter API key not configured');
    }

    const response = await axios.post(
      'https://openrouter.ai/api/v1/chat/completions',
      {
        model,
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content }
        ],
        temperature: 0.3,
        max_tokens: 4000
      },
      {
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json',
          'HTTP-Referer': 'https://travelviz.app',
          'X-Title': 'TravelViz'
        },
        timeout: 60000 // 60 seconds
      }
    );

    const duration = Date.now() - startTime;
    const responseContent = response.data.choices?.[0]?.message?.content || '';
    
    console.log(`✅ ${model} (${scenario}): ${duration}ms, ${responseContent.length} chars`);
    
    return {
      model,
      scenario,
      success: true,
      duration,
      responseLength: responseContent.length
    };

  } catch (error) {
    const duration = Date.now() - startTime;
    let errorMessage = 'Unknown error';
    
    if (axios.isAxiosError(error)) {
      if (error.code === 'ECONNABORTED') {
        errorMessage = 'Timeout';
      } else if (error.response?.status === 400) {
        errorMessage = `Bad Request: ${error.response.data?.error?.message || 'Invalid model or request'}`;
      } else if (error.response?.status === 429) {
        errorMessage = 'Rate limit exceeded';
      } else {
        errorMessage = `HTTP ${error.response?.status}: ${error.response?.statusText}`;
      }
    } else {
      errorMessage = error instanceof Error ? error.message : String(error);
    }
    
    console.log(`❌ ${model} (${scenario}): Failed after ${duration}ms - ${errorMessage}`);
    
    return {
      model,
      scenario,
      success: false,
      duration,
      error: errorMessage
    };
  }
}

async function runComprehensiveTests(): Promise<void> {
  console.log('🚀 Starting Comprehensive Model Testing...\n');
  
  const results: TestResult[] = [];
  
  // Test each model with each scenario
  for (const model of MODELS_TO_TEST) {
    console.log(`\n📋 Testing model: ${model}`);
    console.log('='.repeat(50));
    
    for (const [scenarioKey, scenario] of Object.entries(TEST_SCENARIOS)) {
      const result = await testModel(
        model,
        scenario.name,
        scenario.content,
        scenario.systemPrompt
      );
      
      results.push(result);
      
      // Wait between tests to avoid rate limiting
      await new Promise(resolve => setTimeout(resolve, 2000));
    }
  }
  
  // Generate summary report
  console.log('\n📊 TEST SUMMARY REPORT');
  console.log('='.repeat(60));
  
  const successfulTests = results.filter(r => r.success);
  const failedTests = results.filter(r => !r.success);
  
  console.log(`✅ Successful tests: ${successfulTests.length}/${results.length}`);
  console.log(`❌ Failed tests: ${failedTests.length}/${results.length}`);
  
  if (successfulTests.length > 0) {
    const avgDuration = successfulTests.reduce((sum, r) => sum + r.duration, 0) / successfulTests.length;
    console.log(`⏱️  Average response time: ${Math.round(avgDuration)}ms`);
  }
  
  // Model performance breakdown
  console.log('\n📈 MODEL PERFORMANCE:');
  for (const model of MODELS_TO_TEST) {
    const modelResults = results.filter(r => r.model === model);
    const modelSuccesses = modelResults.filter(r => r.success);
    const successRate = (modelSuccesses.length / modelResults.length) * 100;
    
    console.log(`${model}:`);
    console.log(`  Success rate: ${successRate.toFixed(1)}%`);
    
    if (modelSuccesses.length > 0) {
      const avgTime = modelSuccesses.reduce((sum, r) => sum + r.duration, 0) / modelSuccesses.length;
      console.log(`  Avg response time: ${Math.round(avgTime)}ms`);
    }
    
    const modelFailures = modelResults.filter(r => !r.success);
    if (modelFailures.length > 0) {
      console.log(`  Common errors: ${[...new Set(modelFailures.map(r => r.error))].join(', ')}`);
    }
    console.log('');
  }
  
  // Scenario complexity analysis
  console.log('🎯 SCENARIO ANALYSIS:');
  for (const [scenarioKey, scenario] of Object.entries(TEST_SCENARIOS)) {
    const scenarioResults = results.filter(r => r.scenario === scenario.name);
    const scenarioSuccesses = scenarioResults.filter(r => r.success);
    const successRate = (scenarioSuccesses.length / scenarioResults.length) * 100;
    
    console.log(`${scenario.name}:`);
    console.log(`  Success rate: ${successRate.toFixed(1)}%`);
    
    if (scenarioSuccesses.length > 0) {
      const avgTime = scenarioSuccesses.reduce((sum, r) => sum + r.duration, 0) / scenarioSuccesses.length;
      console.log(`  Avg response time: ${Math.round(avgTime)}ms`);
    }
    console.log('');
  }
  
  // Recommendations
  console.log('💡 RECOMMENDATIONS:');
  const hangingModels = results.filter(r => !r.success && r.error === 'Timeout').map(r => r.model);
  if (hangingModels.length > 0) {
    console.log(`⚠️  Models with timeout issues: ${[...new Set(hangingModels)].join(', ')}`);
    console.log('   Consider removing these from the primary model list');
  }
  
  const reliableModels = MODELS_TO_TEST.filter(model => {
    const modelResults = results.filter(r => r.model === model);
    const successRate = modelResults.filter(r => r.success).length / modelResults.length;
    return successRate >= 0.8; // 80% success rate threshold
  });
  
  if (reliableModels.length > 0) {
    console.log(`✅ Reliable models (>80% success): ${reliableModels.join(', ')}`);
    console.log('   Prioritize these in the model selection logic');
  }
}

// Run the tests
runComprehensiveTests().catch(console.error);
