import { v4 as uuidv4 } from 'uuid';
import * as crypto from 'crypto';
import { logger } from '../utils/logger';
import { enhancedAIRouterService } from './enhanced-ai-router.service';
import { usageTrackingService } from './usage-tracking.service';
import { geocodingService } from './geocoding.service';
import { getSupabaseClient } from '../lib/supabase';
import { cacheService } from './cache.service';
import { redis } from '../config/redis';
import { redisConnectionPool } from './redis-connection-pool.service';
import {
  ParsedTrip,
  ParseSession,
  ParsedTripSchema,
  parsedTripToDbFormat,
  parsedActivityToDbFormat,
  ActivityType,
  normalizeActivityType,
} from '@travelviz/shared';
import { AI_CONFIG } from '../config/ai.config';

/**
 * Circuit Breaker for AI API calls
 */
class AICircuitBreaker {
  private failures: number = 0;
  private lastFailureTime: number = 0;
  private state: 'CLOSED' | 'OPEN' | 'HALF_OPEN' = 'CLOSED';
  private readonly failureThreshold = 3;
  private readonly recoveryTimeout = 60000; // 60 seconds
  private readonly halfOpenTimeout = 30000; // 30 seconds

  async execute<T>(operation: () => Promise<T>): Promise<T> {
    if (this.state === 'OPEN') {
      const now = Date.now();
      if (now - this.lastFailureTime >= this.recoveryTimeout) {
        this.state = 'HALF_OPEN';
        logger.info('AI Circuit breaker transitioning to HALF_OPEN');
      } else {
        throw new Error('AI service temporarily unavailable. Circuit breaker is OPEN.');
      }
    }

    try {
      const result = await operation();
      this.onSuccess();
      return result;
    } catch (error) {
      this.onFailure(error);
      throw error;
    }
  }

  private onSuccess(): void {
    if (this.state === 'HALF_OPEN') {
      this.state = 'CLOSED';
      this.failures = 0;
      logger.info('AI Circuit breaker recovered to CLOSED state');
    }
  }

  private onFailure(error: unknown): void {
    this.failures++;
    this.lastFailureTime = Date.now();

    // Check if it's a rate limit error (don't count against circuit breaker)
    if (error instanceof Error && error.message.includes('Rate limit')) {
      logger.warn('AI rate limit hit, not counting against circuit breaker');
      return;
    }

    if (this.failures >= this.failureThreshold) {
      this.state = 'OPEN';
      logger.error('AI Circuit breaker OPENED due to failures', {
        failures: this.failures,
        recoveryTime: new Date(Date.now() + this.recoveryTimeout).toISOString()
      });
    }
  }

  getState(): string {
    return this.state;
  }

  reset(): void {
    this.failures = 0;
    this.state = 'CLOSED';
    this.lastFailureTime = 0;
  }
}

/**
 * Enhanced AI Parser Service with SSE Progress Updates
 * Provides real-time parsing progress through Redis pub/sub
 */
export class AIParserService {
  private progressChannel = 'parse:progress:';
  private aiCircuitBreaker: AICircuitBreaker;

  constructor() {
    this.aiCircuitBreaker = new AICircuitBreaker();
    // Enhanced AI Router Service handles all AI configuration and model selection

    // Log circuit breaker initial state
    logger.info('AIParserService initialized', {
      circuitBreakerState: this.aiCircuitBreaker.getState(),
      enhancedAIRouterAvailable: true
    });
  }

  /**
   * Validate AI source type
   */
  private validateAISource(source: string): 'chatgpt' | 'claude' | 'gemini' | 'unknown' {
    const validSources = ['chatgpt', 'claude', 'gemini', 'unknown'];
    return validSources.includes(source) 
      ? source as 'chatgpt' | 'claude' | 'gemini' | 'unknown'
      : 'unknown';
  }

  /**
   * Create a parse session and start parsing
   */
  async createParseSession(content: string, source: string, userId: string): Promise<string> {
    // Check for duplicate request
    const existingSessionId = await this.deduplicateRequest(content, userId);
    if (existingSessionId) {
      logger.info('Duplicate request detected, returning existing session', { 
        sessionId: existingSessionId,
        userId 
      });
      return existingSessionId;
    }

    const sessionId = uuidv4();
    
    // Store initial session in database with proper timestamp handling
    const now = new Date().toISOString();
    const { error } = await getSupabaseClient()
      .from('ai_import_logs')
      .insert({
        id: sessionId,
        user_id: userId,
        ai_platform: source,
        import_status: 'processing',
        raw_conversation: content.substring(0, 5000), // Limit stored content
        created_at: now,
        updated_at: now, // FIXED: Ensure updated_at is not before created_at
      });

    if (error) {
      logger.error('Failed to create parse session', { error, sessionId, userId });
      throw new Error('Failed to create parse session');
    }

    logger.info('Parse session created successfully', { 
      sessionId, 
      userId, 
      source,
      contentLength: content.length 
    });

    // Update deduplication cache with actual session ID
    await this.updateDedupeCache(content, userId, sessionId);

    // Start async parsing - don't await to return immediately
    // Use setTimeout to ensure it runs in the background
    setTimeout(() => {
      logger.info('Starting background parse', { sessionId, source });
      this.parseAsync(sessionId, content, source).catch(error => {
        logger.error('Background parsing failed', { sessionId, error });
      });
    }, 100); // Small delay to ensure response is sent first

    // Publish initial progress
    await this.publishProgress(sessionId, 'initializing', 0, AI_CONFIG.progressMessages.initializing);

    return sessionId;
  }

  /**
   * Get parse session status
   */
  async getSession(sessionId: string): Promise<ParseSession | null> {
    const { data, error } = await getSupabaseClient()
      .from('ai_import_logs')
      .select('*')
      .eq('id', sessionId)
      .single();

    if (error || !data) {
      return null;
    }

    // Check for orphaned sessions (processing for more than 10 minutes)
    // Increased from 5 minutes to allow for model fallback attempts
    if (data.import_status === 'processing') {
      const sessionAge = Date.now() - new Date(data.created_at).getTime();
      const maxProcessingTime = 10 * 60 * 1000; // 10 minutes (increased to allow fallback)

      if (sessionAge > maxProcessingTime) {
        logger.warn('Detected orphaned session, marking as failed', {
          sessionId: data.id,
          ageMinutes: Math.round(sessionAge / 60000),
          created: data.created_at,
          updated: data.updated_at
        });

        // Mark as failed due to timeout
        try {
          await getSupabaseClient()
            .from('ai_import_logs')
            .update({
              import_status: 'failed',
              error_message: 'Session timeout - processing took too long or server was restarted',
              updated_at: new Date().toISOString()
            })
            .eq('id', sessionId);

          // Return the failed session
          return {
            id: data.id,
            status: 'error',
            progress: 0,
            currentStep: 'error',
            result: undefined,
            error: 'Session timeout - processing took too long or server was restarted',
            startedAt: new Date(data.created_at),
            completedAt: new Date(),
          };
        } catch (updateError) {
          logger.error('Failed to mark orphaned session as failed', { sessionId, updateError });
        }
      }
    }

    // Map database fields to ParseSession type
    return {
      id: data.id,
      status: (data.import_status === 'success' || data.import_status === 'complete') ? 'complete' :
              data.import_status === 'failed' ? 'error' :
              data.import_status === 'processing' ? 'processing' : 'pending',
      progress: (data.import_status === 'success' || data.import_status === 'complete') ? 100 : 50,
      currentStep: data.import_status === 'processing' ? 'parsing' : 'complete',
      result: data.parsed_data as ParsedTrip | undefined,
      error: data.error_message || undefined,
      startedAt: new Date(data.created_at),
      completedAt: (data.import_status === 'success' || data.import_status === 'complete' || data.import_status === 'failed') ? new Date() : undefined,
    };
  }

  /**
   * Check for duplicate requests using content-based fingerprinting
   * Returns existing sessionId if found, null otherwise
   */
  private async deduplicateRequest(content: string, userId: string): Promise<string | null> {
    // Create content hash
    const hash = crypto.createHash('sha256')
      .update(content)
      .update(userId)
      .digest('hex');
    
    const dedupeKey = `parse:dedupe:${hash}`;
    
    return redisConnectionPool.execute(async (redis) => {
      // Try to get existing session ID
      const existingSessionId = await redis.get(dedupeKey);
      
      if (existingSessionId && typeof existingSessionId === 'string') {
        // Check if session is still valid
        const session = await this.getSession(existingSessionId);
        if (session && session.status !== 'error') {
          // Check if session is too old (more than 10 minutes)
          const sessionAge = Date.now() - session.startedAt.getTime();
          const maxAge = 10 * 60 * 1000; // 10 minutes

          if (sessionAge > maxAge) {
            logger.info('Found old session, marking as failed and creating new one', {
              sessionId: existingSessionId,
              status: session.status,
              ageMinutes: Math.round(sessionAge / 60000)
            });

            // Mark old session as failed
            try {
              await getSupabaseClient()
                .from('ai_import_logs')
                .update({
                  import_status: 'failed',
                  error_message: 'Session timeout - too old',
                  updated_at: new Date().toISOString()
                })
                .eq('id', existingSessionId);
            } catch (error) {
              logger.error('Failed to mark old session as failed', { sessionId: existingSessionId, error });
            }

            // Remove from Redis cache
            await redis.del(dedupeKey);
            return null; // Create new session
          }

          logger.info('Found existing parse session', {
            sessionId: existingSessionId,
            status: session.status
          });
          return existingSessionId;
        }
      }
      
      // No valid existing session found
      // Store placeholder to prevent race conditions
      const tempSessionId = `pending_${uuidv4()}`;
      const result = await redis.set(dedupeKey, tempSessionId, {
        nx: true,  // Only set if not exists
        ex: 500    // 500ms TTL for race condition prevention
      });
      
      if (result === null) {
        // Another request set the key, wait and retry
        await new Promise(resolve => setTimeout(resolve, 100));
        const retrySessionId = await redis.get(dedupeKey);
        if (retrySessionId && typeof retrySessionId === 'string' && !retrySessionId.startsWith('pending_')) {
          return retrySessionId;
        }
      }
      
      // We won the race, will create new session
      // Update the key with longer TTL after session is created
      return null;
    });
  }
  
  /**
   * Update deduplication cache with actual session ID
   */
  private async updateDedupeCache(content: string, userId: string, sessionId: string): Promise<void> {
    const hash = crypto.createHash('sha256')
      .update(content)
      .update(userId)
      .digest('hex');
    
    const dedupeKey = `parse:dedupe:${hash}`;
    
    await redisConnectionPool.execute(async (redis) => {
      // Store actual session ID with 5 minute TTL
      await redis.set(dedupeKey, sessionId, {
        ex: 300  // 5 minutes
      });
    });
  }

  /**
   * Parse content asynchronously with progress updates
   */
  private async parseAsync(
    sessionId: string,
    content: string,
    source: string
  ): Promise<void> {
    let currentStep = 'initializing';
    const PARSE_TIMEOUT = 10 * 60 * 1000; // 10 minutes total timeout (increased to allow model fallback)

    // Wrap the entire parsing process in a timeout
    const parsePromise = this.executeParseWithTimeout(sessionId, content, source);
    const timeoutPromise = new Promise<never>((_, reject) => {
      setTimeout(() => {
        reject(new Error(`Parse session timeout after ${PARSE_TIMEOUT}ms`));
      }, PARSE_TIMEOUT);
    });

    try {
      await Promise.race([parsePromise, timeoutPromise]);
    } catch (error) {
      logger.error('Parse failed with timeout or error', { 
        sessionId, 
        error: error instanceof Error ? error.message : String(error)
      });
      
      // Ensure session is marked as failed
      try {
        await this.updateSessionStatus(sessionId, 'failed', 0, 'error', 
          error instanceof Error ? error.message : 'Unknown error occurred during parsing');
      } catch (statusError) {
        logger.error('Failed to update session status after parse error', { sessionId, statusError });
      }
      
      // Don't re-throw to prevent unhandled promise rejection
    }
  }

  /**
   * Execute the actual parsing logic with detailed logging
   */
  private async executeParseWithTimeout(
    sessionId: string,
    content: string,
    source: string
  ): Promise<void> {
    let currentStep = 'initializing';

    try {
      logger.info('parseAsync started', { sessionId, contentLength: content.length, source });
      
      // Step 1: Initializing (10%)
      currentStep = 'initializing';
      await this.updateSessionStatus(sessionId, 'processing', 10, 'initializing', 'Setting up AI parsing session...');

      // Enhanced AI Router Service will handle all model selection and logging
      logger.info('Starting AI parse with Enhanced AI Router Service', {
        sessionId,
        contentLength: content.length,
        source,
        hasGeminiKey: !!process.env.GOOGLE_GEMINI_API_KEY,
        hasOpenRouterKey: !!process.env.OPENROUTER_API_KEY
      });

      // Step 2: Extracting (20%)
      currentStep = 'extracting';
      await this.updateSessionStatus(sessionId, 'processing', 20, 'extracting', 'Reading and processing your content...');

      // Step 3: AI Parsing (40%) - Using Enhanced AI Router Service
      currentStep = 'parsing';
      await this.updateSessionStatus(sessionId, 'processing', 40, 'parsing', 'AI is analyzing your travel plans...');

      logger.info('Using Enhanced AI Router for intelligent model selection', {
        source,
        contentLength: content.length,
        sessionId
      });

      // Add intermediate progress update during AI call
      const progressUpdateInterval = setInterval(async () => {
        await this.updateSessionStatus(sessionId, 'processing', 45, 'parsing', 'AI is still analyzing your content...');
      }, 15000); // Update every 15 seconds during AI call

      let parsedData;
      try {
        // Use Enhanced AI Router Service with intelligent model selection and usage tracking
        // Requirements 2.1, 2.2, 5.1, 5.2: Prioritize free tier, handle rate limits, fallbacks
        // Enhanced AI Router Service handles all fallbacks internally - no legacy system needed
        parsedData = await this.aiCircuitBreaker.execute(
          () => enhancedAIRouterService.parseContent(content, source, sessionId)
        );
      } catch (enhancedRouterError) {
        logger.error('Enhanced AI Router Service failed - no fallback available', {
          error: enhancedRouterError instanceof Error ? enhancedRouterError.message : String(enhancedRouterError),
          sessionId
        });
        throw enhancedRouterError;
      } finally {
        clearInterval(progressUpdateInterval);
      }

      logger.info('AI API returned', {
        hasData: !!parsedData,
        metadataSource: parsedData?.metadata?.source,
        activities: parsedData?.activities?.length || 0
      });

      // Step 4: Finding Locations (60%)
      currentStep = 'locations';
      await this.updateSessionStatus(sessionId, 'processing', 60, 'locations', 'Identifying destinations and places...');

      // Enhance with geocoding
      const enhancedData = await this.enhanceWithGeocoding(parsedData);

      // Step 5: Processing Dates (80%)
      currentStep = 'dates';
      await this.updateSessionStatus(sessionId, 'processing', 80, 'dates', 'Organizing timeline and schedule...');

      // Validate parsed data
      const validatedData = ParsedTripSchema.parse(enhancedData);

      // Step 6: Finalizing (95%)
      currentStep = 'finalizing';
      await this.updateSessionStatus(sessionId, 'processing', 95, 'finalizing', 'Completing your itinerary...');

      // Store success result and update status in single atomic operation
      // Include model usage tracking per design.md enhanced ai_import_logs
      try {
        const { error: updateError } = await getSupabaseClient()
          .from('ai_import_logs')
          .update({
            import_status: 'complete',
            parsed_data: validatedData,
            model_used: parsedData?.metadata?.modelUsed || 'unknown',
            input_tokens: parsedData?.metadata?.inputTokens || 0,
            output_tokens: parsedData?.metadata?.outputTokens || 0,
            processing_cost: parsedData?.metadata?.cost || 0,
            fallback_attempts: parsedData?.metadata?.fallbackAttempts || 0,
            updated_at: new Date().toISOString(),
          })
          .eq('id', sessionId);

        if (updateError) {
          logger.error('Failed to update parse session to complete', { sessionId, updateError });
          // Try to mark as failed instead of leaving in processing state
          await this.updateSessionStatus(sessionId, 'failed', 95, 'error', 'Failed to save parsing results');
          throw new Error(`Failed to update parse session: ${updateError.message}`);
        }

        logger.info('Parse session updated to complete in database', { sessionId });
      } catch (dbError) {
        logger.error('Failed to update parse session to complete', { sessionId, dbError });
        // Ensure session doesn't remain in processing state
        try {
          await this.updateSessionStatus(sessionId, 'failed', 95, 'error', 'Database save error');
        } catch (fallbackError) {
          logger.error('Failed to mark session as failed after database error', { sessionId, fallbackError });
        }
        throw new Error('Failed to save parsing results to database');
      }

      // Publish final progress update
      await this.publishProgress(sessionId, 'complete', 100, 'Your itinerary is ready!');

      logger.info('Parse completed successfully', { sessionId });

    } catch (error) {
      // Log detailed error information
      logger.error('Parse failed - updating status', { 
        sessionId, 
        currentStep,
        error: error instanceof Error ? {
          message: error.message,
          stack: error.stack,
          name: error.name
        } : error 
      });

      // Determine error message
      let errorMessage = 'Unknown error occurred during parsing';
      if (error instanceof Error) {
        errorMessage = error.message;

        if (error.message.includes('Rate limit')) {
          errorMessage = 'AI service rate limit reached. Please try again in a few minutes.';
        } else if (error.message.includes('timeout') || error.message.includes('timed out')) {
          errorMessage = 'AI processing timed out. Your content may be too complex. Try breaking it into smaller sections.';
        } else if (error.message.includes('Invalid')) {
          errorMessage = 'Invalid format detected. Please check your input and try again.';
        } else if (error.message.includes('Circuit breaker')) {
          errorMessage = 'AI service is temporarily unavailable. Please try again in a few minutes.';
        } else if (error.message.includes('ECONNABORTED') || error.message.includes('ENOTFOUND')) {
          errorMessage = 'Network connection error. Please check your internet connection and try again.';
        } else if (error.message.includes('401') || error.message.includes('Unauthorized')) {
          errorMessage = 'AI service authentication error. Please contact support.';
        } else if (error.message.includes('429')) {
          errorMessage = 'Too many requests. Please wait a moment and try again.';
        } else if (error.message.includes('500') || error.message.includes('502') || error.message.includes('503')) {
          errorMessage = 'AI service is temporarily down. Please try again later.';
        }
      }

      // Always update session status on error - CRITICAL FIX
      try {
        await this.publishProgress(sessionId, 'error', 0, errorMessage);
        
        // Use the improved updateSessionStatus method for consistency
        await this.updateSessionStatus(sessionId, 'failed', 0, 'error', errorMessage);
        
        logger.info('Session marked as failed successfully', { 
          sessionId, 
          errorMessage,
          currentStep 
        });
      } catch (statusUpdateError) {
        logger.error('Critical: Could not update parse session status after error', {
          sessionId,
          statusUpdateError,
          originalError: errorMessage
        });
        
        // Last resort: direct database update
        try {
          await getSupabaseClient()
            .from('ai_import_logs')
            .update({
              import_status: 'failed',
              error_message: `Critical error: ${errorMessage}`,
              updated_at: new Date().toISOString()
            })
            .eq('id', sessionId);
        } catch (finalError) {
          logger.error('Final attempt to update session failed', { sessionId, finalError });
        }
      }
      
      // Don't re-throw - we've handled the error by updating the session
      // This prevents unhandled promise rejections in the background job
    }
  }

  /**
   * Publish progress update via Redis pub/sub
   */
  private async publishProgress(
    sessionId: string,
    step: string,
    progress: number,
    message: string
  ): Promise<void> {
    try {
      const channel = `${this.progressChannel}${sessionId}`;
      
      const update = {
        sessionId,
        step,
        progress,
        message,
        timestamp: new Date().toISOString(),
      };

      // Publish to Redis channel
      await redis.publish(channel, JSON.stringify(update));
      
      // Also store latest progress in cache for fallback
      await cacheService.set(
        `progress:${sessionId}`,
        update,
        { ttl: 300 } // 5 minutes TTL
      );

      logger.debug('Published progress update', { sessionId, step, progress });
    } catch (error) {
      logger.error('Failed to publish progress', { sessionId, error });
      // Don't throw - progress updates are not critical
    }
  }

  // Legacy callAIAPI method removed - Enhanced AI Router Service handles all AI calls



  // Legacy callAIAPIWithComplexityFallback method removed - Enhanced AI Router Service handles all fallbacks

  // Legacy callAIAPIWithFallback method removed - Enhanced AI Router Service handles all fallbacks

  // Legacy buildPrompt method removed - Enhanced AI Router Service handles all prompt building

  /**
   * Enhance parsed data with real geocoding
   */
  private async enhanceWithGeocoding(data: ParsedTrip): Promise<ParsedTrip> {
    if (!geocodingService.isAvailable()) {
      logger.warn('Geocoding service not available, using fallback coordinates');
      return this.addFallbackCoordinates(data);
    }

    try {
      // Collect all unique locations to geocode
      const locationsToGeocode = new Set<string>();
      
      // Add destination
      if (data.destination) {
        locationsToGeocode.add(data.destination);
      }

      // Add activity locations
      data.activities.forEach(activity => {
        if (activity.location?.address) {
          locationsToGeocode.add(activity.location.address);
        }
      });

      // Geocode locations in batches to avoid timeouts
      const GEOCODE_BATCH_SIZE = 10;
      const locations = Array.from(locationsToGeocode);
      const geocodeResults = new Map<string, { lat: number; lng: number; formatted: string }>();
      
      // Process in batches
      for (let i = 0; i < locations.length; i += GEOCODE_BATCH_SIZE) {
        const batch = locations.slice(i, i + GEOCODE_BATCH_SIZE);
        const batchResults = await geocodingService.geocodeBatch(batch);
        
        // Merge results
        batchResults.forEach((value, key) => {
          if (value !== null) {
            geocodeResults.set(key, value);
          }
        });
        
        // Log progress
        logger.info('Geocoding progress', {
          batchIndex: Math.floor(i / GEOCODE_BATCH_SIZE) + 1,
          totalBatches: Math.ceil(locations.length / GEOCODE_BATCH_SIZE),
          locationsProcessed: Math.min(i + GEOCODE_BATCH_SIZE, locations.length),
          totalLocations: locations.length
        });
      }

      // Apply geocoding results to activities
      const enhancedActivities = data.activities.map(activity => {
        if (!activity.location?.address) {
          // No address, use destination as fallback
          const destResult = geocodeResults.get(data.destination);
          if (destResult) {
            return {
              ...activity,
              location: {
                address: data.destination,
                lat: destResult.lat + (Math.random() - 0.5) * 0.01, // Small variation
                lng: destResult.lng + (Math.random() - 0.5) * 0.01,
                confidence: 0.7
              }
            };
          }
          return activity;
        }

        // Has address, try to geocode it
        const result = geocodeResults.get(activity.location.address);
        if (result) {
          return {
            ...activity,
            location: {
              address: result.formatted,
              lat: result.lat,
              lng: result.lng,
              confidence: 0.9
            }
          };
        }

        // Geocoding failed, keep original or add destination coords
        const destResult = geocodeResults.get(data.destination);
        if (destResult) {
          return {
            ...activity,
            location: {
              ...activity.location,
              lat: destResult.lat + (Math.random() - 0.5) * 0.02,
              lng: destResult.lng + (Math.random() - 0.5) * 0.02,
              confidence: 0.5
            }
          };
        }

        return activity;
      });

      return {
        ...data,
        activities: enhancedActivities
      };

    } catch (error) {
      logger.error('Geocoding enhancement failed', { error });
      return this.addFallbackCoordinates(data);
    }
  }

  /**
   * Add fallback coordinates when geocoding fails
   */
  private addFallbackCoordinates(data: ParsedTrip): ParsedTrip {
    // Common city coordinates for fallback
    const cityCoords: Record<string, { lat: number; lng: number }> = {
      'paris': { lat: 48.8566, lng: 2.3522 },
      'london': { lat: 51.5074, lng: -0.1278 },
      'tokyo': { lat: 35.6762, lng: 139.6503 },
      'new york': { lat: 40.7128, lng: -74.0060 },
      'rome': { lat: 41.9028, lng: 12.4964 },
      'barcelona': { lat: 41.3851, lng: 2.1734 },
      'amsterdam': { lat: 52.3676, lng: 4.9041 },
      'bangkok': { lat: 13.7563, lng: 100.5018 },
      'singapore': { lat: 1.3521, lng: 103.8198 },
      'dubai': { lat: 25.2048, lng: 55.2708 },
    };

    const destLower = data.destination.toLowerCase();
    let baseCoords = { lat: 0, lng: 0 };

    // Find matching city
    for (const [city, coords] of Object.entries(cityCoords)) {
      if (destLower.includes(city)) {
        baseCoords = coords;
        break;
      }
    }

    const enhancedActivities = data.activities.map(activity => {
      if (!activity.location || !activity.location.lat) {
        return {
          ...activity,
          location: {
            address: activity.location?.address || data.destination,
            lat: baseCoords.lat + (Math.random() - 0.5) * 0.1,
            lng: baseCoords.lng + (Math.random() - 0.5) * 0.1,
            confidence: 0.6
          }
        };
      }
      return activity;
    });

    return {
      ...data,
      activities: enhancedActivities
    };
  }

  /**
   * Create trip from parsed data
   */
  async createTripFromParse(
    sessionId: string, 
    userId: string,
    edits?: Partial<ParsedTrip>
  ): Promise<string> {
    // Get parse session
    const session = await this.getSession(sessionId);
    if (!session || session.status !== 'complete' || !session.result) {
      throw new Error('Parse session not found or incomplete');
    }

    // Apply any edits
    const tripData = edits ? { ...session.result, ...edits } : session.result;

    // Create trip in database
    const tripId = uuidv4();
    const dbTrip = parsedTripToDbFormat(tripData, userId);

    const { error: tripError } = await getSupabaseClient()
      .from('trips')
      .insert({
        id: tripId,
        ...dbTrip,
        visibility: 'private',
        status: 'planning',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      });

    if (tripError) {
      logger.error('Failed to create trip', { tripError });
      throw new Error('Failed to create trip');
    }

    // Create activities
    if (tripData.activities.length > 0) {
      const activities = tripData.activities.map((activity, index) => ({
        id: uuidv4(),
        ...parsedActivityToDbFormat(activity, tripId, index),
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      }));

      const { error: activitiesError } = await getSupabaseClient()
        .from('activities')
        .insert(activities);

      if (activitiesError) {
        logger.error('Failed to create activities', { activitiesError });
        // Continue anyway - trip is created
      }
    }

    // Update import log with trip ID
    await getSupabaseClient()
      .from('ai_import_logs')
      .update({
        trip_id: tripId,
      })
      .eq('id', sessionId);

    return tripId;
  }

  /**
   * Update session status in database
   */
  private async updateSessionStatus(
    sessionId: string,
    status: string,
    progress: number,
    currentStep: string,
    message: string
  ): Promise<void> {
    try {
      // FIXED: Always update status to prevent orphaned sessions
      // Use atomic update with proper timestamp handling
      const updateData: any = {
        import_status: status,
        updated_at: new Date().toISOString(),
      };

      // Add error message for failed status
      if (status === 'failed' && message) {
        updateData.error_message = message;
      }

      // Add parsed data for complete status
      if (status === 'complete' && currentStep === 'complete') {
        // This will be handled separately in the main completion logic
        logger.debug('Complete status update - parsed data handled separately', { sessionId });
      }

      const { error } = await getSupabaseClient()
        .from('ai_import_logs')
        .update(updateData)
        .eq('id', sessionId);

      if (error) {
        logger.error('Failed to update session status', { sessionId, status, error });
        // Don't throw - continue with progress publishing
      } else {
        logger.info('Session status updated successfully', { 
          sessionId, 
          status, 
          progress,
          currentStep 
        });
      }

      // Always publish progress for real-time updates
      await this.publishProgress(sessionId, currentStep, progress, message);
    } catch (error) {
      logger.error('Error updating session status', { sessionId, status, error });
      // Don't throw - this is a background operation
    }
  }

  /**
   * Get AI circuit breaker status
   */
  getCircuitBreakerStatus(): { state: string; isAvailable: boolean } {
    const state = this.aiCircuitBreaker.getState();
    return {
      state,
      isAvailable: state !== 'OPEN'
    };
  }
}

// Export singleton instance with lazy initialization
let _aiParserService: AIParserService | null = null;

export const getAIParserService = (): AIParserService => {
  if (!_aiParserService) {
    logger.info('Creating AIParserService', {
      hasGeminiKey: !!process.env.GOOGLE_GEMINI_API_KEY,
      hasOpenRouterKey: !!process.env.OPENROUTER_API_KEY,
      nodeEnv: process.env.NODE_ENV
    });
    _aiParserService = new AIParserService();
  }
  return _aiParserService;
};

// For backward compatibility, provide a getter that lazily initializes
export const aiParserService = {
  get instance(): AIParserService {
    return getAIParserService();
  }
};

