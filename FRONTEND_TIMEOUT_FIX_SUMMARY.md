# Frontend Timeout Issue Fix Summary

## Problem Analysis

**Root Cause**: Multiple critical issues causing the timeout problem:

1. **Backend Code Bug**: Undefined variable `aiResponse` in OpenRouter API call causing infinite hang
2. **Frontend Timeout Too Short**: 30 seconds vs actual 30.4 second processing time
3. **Frontend Polling Bug**: Circular dependency causing rapid polling (150-200ms intervals)

**Evidence from OpenRouter Log**:
- API call started: 20:42:02.236Z
- API call completed: 20:42:36.573Z
- Generation time: 30,439ms (30.4 seconds)
- Frontend timeout: 30,000ms (30 seconds)

**Backend Hang Evidence**:
- Logs stop after "Enhanced AI Router Service: Prompts retrieved"
- No "OpenRouter API response received" log
- Process hangs indefinitely at OpenRouter API call

## Fixes Implemented

### 1. **CRITICAL**: Fixed Backend OpenRouter API Bug

**File**: `packages/hub/src/services/enhanced-ai-router.service.ts` (line 562-599)

**Problem**: Undefined variable `aiResponse` causing infinite hang in OpenRouter API call.

**Fix**: Added proper response extraction and error handling.

```typescript
// Before (BROKEN - caused infinite hang)
logger.info('OpenRouter API call successful', {
  modelId,
  responseLength: aiResponse.length  // ❌ aiResponse undefined!
});
return { content: aiResponse };  // ❌ aiResponse undefined!

// After (FIXED)
const aiResponse = response.data?.choices?.[0]?.message?.content;
if (!aiResponse) {
  throw new Error('No response from OpenRouter API');
}
return { content: aiResponse };
```

### 2. Frontend API Client Timeout Increase

**Files Modified**:
- `packages/web/lib/api-client.ts` (line 41)
- `packages/web/lib/api-client-with-circuit-breaker.ts` (line 41)  
- `packages/web/src/lib/api.ts` (line 105)

**Change**: Increased timeout from 30 seconds to 90 seconds

```typescript
// Before
this.timeout = config.timeout || 30000;

// After  
this.timeout = config.timeout || 90000; // Increased to 90 seconds for long-running AI operations
```

### 3. Fixed Rapid Polling Issue

**File**: `packages/web/hooks/useImportStatus.ts` (multiple fixes)

**Problem 1**: Retry logic causing rapid polling
**Problem 2**: Circular dependency in useCallback creating multiple polling instances

**Fixes**:
1. Use pollingInterval for retry delays (line 161-164)
2. Remove circular dependency in startPolling (line 218)
3. Add polling cleanup to prevent multiple instances (line 185-189)

```typescript
// Before
retryTimeoutRef.current = setTimeout(() => {
  checkStatus();
}, RETRY_DELAY);

// After
retryTimeoutRef.current = setTimeout(() => {
  checkStatus();  
}, pollingInterval); // Use pollingInterval instead of RETRY_DELAY to maintain consistent polling
```

### 3. Enhanced Error Handling

**File**: `packages/web/hooks/useImportStatus.ts` (line 141-165)

**Improvement**: Better distinction between timeout errors and network errors with more descriptive messages.

```typescript
// Check if this is a timeout error vs other network error
const isTimeoutError = error instanceof Error && 
  (error.message.includes('timeout') || error.message.includes('aborted'));

const errorMessage = isTimeoutError 
  ? 'Request timed out. The AI processing is taking longer than expected. Please try again.'
  : 'Failed to check import status. Please try again.';
```

### 4. Updated User Expectations

**File**: `packages/web/components/import/steps/ParsingStep.tsx` (line 157-159)

**Change**: Updated user-facing message to reflect new timeout expectations.

```typescript
// Before
"This usually takes 30-60 seconds. We're extracting destinations, activities, and dates."

// After
"This usually takes 30-90 seconds. We're extracting destinations, activities, and dates."
```

### 5. Enhanced Backend Logging

**File**: `packages/hub/src/services/enhanced-ai-router.service.ts` (line 169-180)

**Addition**: Added timeout trigger logging for better debugging.

```typescript
setTimeout(() => {
  logger.warn('Enhanced AI Router Service: Model execution timeout triggered', {
    modelId,
    timeoutMs: EXECUTION_TIMEOUT,
    elapsedMs: Date.now() - startTime
  });
  reject(new Error(`Model execution timeout after ${EXECUTION_TIMEOUT}ms for model ${modelId}`));
}, EXECUTION_TIMEOUT);
```

## Expected Results

1. **No more premature timeouts**: Frontend will wait up to 90 seconds for API responses
2. **Consistent polling**: Status checks every 5 seconds instead of rapid polling
3. **Better error messages**: Users will see more descriptive timeout vs network error messages
4. **Improved debugging**: Backend logs will show when actual timeouts occur

## Verification Steps

1. Test PDF import with complex content (20+ page documents)
2. Monitor network tab to confirm 5-second polling intervals
3. Verify API calls complete successfully within 90-second window
4. Check that error messages are appropriate for different failure types

## Timeout Configuration Summary

| Component | Previous | New | Reason |
|-----------|----------|-----|---------|
| Frontend API Client | 30s | 90s | Match backend processing time |
| Backend REQUEST_TIMEOUT | 60s | 60s | No change needed |
| Backend EXECUTION_TIMEOUT | 120s | 120s | No change needed |
| Frontend Polling Max | 5min | 5min | No change needed |
| Frontend Polling Interval | 5s | 5s | Fixed implementation bug |

The fix ensures frontend timeouts are aligned with actual backend processing times while maintaining reasonable upper bounds for user experience.
