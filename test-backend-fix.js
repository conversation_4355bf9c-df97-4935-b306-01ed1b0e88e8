#!/usr/bin/env node

/**
 * Quick test to verify the backend OpenRouter API fix
 */

const axios = require('axios');
const path = require('path');

// Load environment variables
require('dotenv').config({ path: path.join(__dirname, 'packages/hub/.env.local') });

const API_BASE_URL = 'http://localhost:3001';

async function testBackendFix() {
  console.log('🧪 Testing Backend OpenRouter API Fix...\n');

  try {
    // Test content (shorter for quick testing)
    const testContent = `
Day 1: London
- Arrive at Heathrow Airport
- Check into hotel near Tower Bridge
- Visit Tower of London
- Walk across Tower Bridge
- Dinner at Borough Market

Day 2: London
- Visit British Museum
- Lunch in Covent Garden
- See a West End show
- Evening at pub in Soho
    `.trim();

    console.log('1. Creating parse session...');
    const parseResponse = await axios.post(
      `${API_BASE_URL}/api/v1/import/parse-simple`,
      {
        content: testContent,
        source: 'chatgpt'
      },
      {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer test-token`
        },
        timeout: 10000
      }
    );

    const sessionId = parseResponse.data.data.importId;
    console.log(`✅ Session created: ${sessionId}\n`);

    // Monitor for completion
    console.log('2. Monitoring parsing progress...');
    const startTime = Date.now();
    let attempts = 0;
    let lastStatus = 'pending';

    while (Date.now() - startTime < 120000) { // 2 minutes max
      attempts++;
      
      try {
        const statusResponse = await axios.get(
          `${API_BASE_URL}/api/v1/import/parse-simple/${sessionId}`,
          {
            headers: {
              'Authorization': `Bearer test-token`
            },
            timeout: 10000
          }
        );

        const { status, progress, currentStep, error } = statusResponse.data.data;
        
        if (status !== lastStatus) {
          console.log(`📊 Status: ${status} (${progress}%) - ${currentStep}`);
          lastStatus = status;
        }

        if (status === 'complete') {
          console.log('\n✅ Parsing completed successfully!');
          console.log(`⏱️  Total time: ${Math.round((Date.now() - startTime) / 1000)}s`);
          console.log(`🔄 Total attempts: ${attempts}`);
          console.log('\n🎉 Backend fix verification PASSED!');
          return;
        }

        if (status === 'error') {
          console.log(`\n❌ Parsing failed: ${error}`);
          console.log('🔍 This suggests the backend fix may not be working correctly');
          return;
        }

      } catch (pollError) {
        console.log(`⚠️  Polling error (attempt ${attempts}):`, pollError.message);
      }

      // Wait 2 seconds before next poll
      await new Promise(resolve => setTimeout(resolve, 2000));
    }

    console.log('\n⏰ Test timed out after 2 minutes');
    console.log(`🔄 Total attempts: ${attempts}`);
    console.log('🔍 This may indicate the backend is still hanging');

  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
    
    if (error.code === 'ECONNABORTED') {
      console.log('🔍 This indicates a timeout - check if the server is running');
    } else if (error.response) {
      console.log('🔍 Server responded with:', error.response.status, error.response.data);
    }
  }
}

// Run the test
testBackendFix().catch(console.error);
