PS C:\Users\<USER>\Travelviz\Travelviz> pnpm dev

> travelviz@1.0.0 dev C:\Users\<USER>\Travelviz\Travelviz
> node scripts/dev-services-enhanced.js

Starting TravelViz development servers...
[hub] Starting API server... 
[web] Starting Next.js app...

⠴ Waiting for services to start...[hub] > @travelviz/hub@1.0.0 dev C:\Users\<USER>\Travelviz\Travelviz\packages\hub
[hub] 🔄 Compiling TypeScript...
[hub] > npx tsx watch src/index.ts
[web] > @travelviz/web@1.0.0 dev C:\Users\<USER>\Travelviz\Travelviz\packages\web
[web] > next dev
⠦ Waiting for services to start...[web]    ▲ Next.js 15.3.5
[web]    - Local:        http://localhost:3000    
[web]    - Network:      http://*************:3000
[web]    - Environments: .env.local
[web] 🔄 Initializing Next.js...
⠦ Waiting for services to start...[hub] 2025-07-17T21:14:54.491Z [WARN] Travelpayouts API key not configured 
⠇ Waiting for services to start...[web]  ✓ Ready in 2.6s
[web]  ✓ Compiled /middleware in 195ms
⠏ Waiting for services to start...[hub] 2025-07-17T21:14:54.805Z [WARN] Mapbox access token not found - geocoding will be disabled 
[web]  ✓ Compiled (108 modules)
⠋ Waiting for services to start...[hub] 2025-07-17T21:14:54.852Z [INFO] Usage tracking service initialized with reset notification listener 
[hub] 2025-07-17T21:14:54.855Z [INFO] Model-specific prompts initialized {
[hub]   "promptCount": 5,
[hub]   "models": [
[hub]     "moonshotai/kimi-k2:free",
[hub]     "google/gemini-2.5-pro",
[hub]     "google/gemini-2.5-flash",
[hub]     "google/gemini-2.0-flash",
[hub]     "openai/gpt-4.1-nano"
[hub]   ]
[hub] }
[web] 🔍 Middleware: No auth cookie found for /
[web] 🔍 Middleware: No auth cookie found for /
[web] 🔍 Middleware: No auth cookie found for /
[web] 🔍 Middleware: No auth cookie found for /
[web] 🔍 Middleware: No auth cookie found for /
[web] 🔍 Middleware: No auth cookie found for /
⠸ Waiting for services to start...[hub] 2025-07-17T21:14:55.234Z [INFO] Loading environment variables... {
[hub]   "cwd": "C:\\Users\\<USER>\\Travelviz\\Travelviz\\packages\\hub",
[hub]   "__dirname": "C:\\Users\\<USER>\\Travelviz\\Travelviz\\packages\\hub\\src\\utils",
[hub]   "rootDir": "C:\\Users\\<USER>\\Travelviz\\Travelviz\\packages\\hub",
[hub]   "nodeEnv": "development"
[hub] }
[hub] 2025-07-17T21:14:55.234Z [INFO] Checking for environment file: C:\Users\<USER>\Travelviz\Travelviz\packages\hub\.env.local
[hub] 2025-07-17T21:14:55.234Z [INFO] Found environment file: C:\Users\<USER>\Travelviz\Travelviz\packages\hub\.env.local
[hub] 2025-07-17T21:14:55.235Z [INFO] Environment loaded from: C:\Users\<USER>\Travelviz\Travelviz\packages\hub\.env.local
[hub] 2025-07-17T21:14:55.236Z [INFO] Environment status: {
[hub]   "NODE_ENV": "development",
[hub]   "OPENROUTER_API_KEY": true,
[hub]   "GOOGLE_GEMINI_API_KEY": true,
[hub]   "SUPABASE_URL": true,
[hub]   "UPSTASH_REDIS_URL": true
[hub] }
[hub] 2025-07-17T21:14:55.236Z [INFO] Environment key details: {
[hub]   "OPENROUTER_API_KEY": "Set (length: 73)",
[hub]   "GOOGLE_GEMINI_API_KEY": "Set (length: 39)",
[hub]   "SUPABASE_URL": "Set (length: 40)",
[hub]   "SUPABASE_SERVICE_ROLE_KEY": "Set (length: 219)",
[hub]   "UPSTASH_REDIS_URL": "Set (length: 34)",
[hub]   "JWT_SECRET": "Set (length: 60)",
[hub]   "MAPBOX_ACCESS_TOKEN": "Set (length: 92)",
[hub]   "GOOGLE_PLACES_API_KEY": "Set (length: 39)"
[hub] }
[hub] 2025-07-17T21:14:55.236Z [INFO] Environment variable JWT_SECRET is set (value hidden)
[hub] 2025-07-17T21:14:55.236Z [INFO] Environment variable SUPABASE_URL is set
[hub] 2025-07-17T21:14:55.236Z [INFO] Environment variable SUPABASE_SERVICE_ROLE_KEY is set (value hidden)
[hub] 2025-07-17T21:14:55.236Z [INFO] Environment variable SUPABASE_JWT_SECRET is set (value hidden)
[hub] 2025-07-17T21:14:55.236Z [INFO] Environment variable OPENROUTER_API_KEY is set (value hidden)
[hub] 2025-07-17T21:14:55.236Z [INFO] Environment variable MAPBOX_ACCESS_TOKEN is set (value hidden)
[hub] 2025-07-17T21:14:55.236Z [INFO] Environment variable GOOGLE_PLACES_API_KEY is set (value hidden)
[hub] 2025-07-17T21:14:55.236Z [INFO] Environment variable PORT is set
[hub] 2025-07-17T21:14:55.236Z [INFO] Environment variable NODE_ENV is set
[hub] 2025-07-17T21:14:55.236Z [INFO] JWT_SECRET validation passed
[hub] 2025-07-17T21:14:55.236Z [INFO] MAPBOX_ACCESS_TOKEN validation passed
[hub] 2025-07-17T21:14:55.236Z [INFO] GOOGLE_PLACES_API_KEY validation passed
[hub] 2025-07-17T21:14:55.236Z [INFO] All required environment variables are properly configured
[hub] 2025-07-17T21:14:55.245Z [INFO] 🚀 TravelViz Hub API server running on port 3001
[hub] 2025-07-17T21:14:55.245Z [INFO] 📍 Health check: http://localhost:3001/health
[hub] 2025-07-17T21:14:55.245Z [INFO] 🔧 Environment: development
[hub] 2025-07-17T21:14:55.245Z [INFO] 🌐 CORS enabled for: http://localhost:3000
⠴ Waiting for services to start...✅ [hub] Ready in 5.1s - http://localhost:3001
[web] 🔨 Compiling pages...
[web]  ○ Compiling / ...
[hub] 2025-07-17T21:14:55.423Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/health",
[hub]   "statusCode": 200,
[hub]   "responseTime": "1.862ms",
[hub]   "ip": "::1"
[hub] }
[hub] GET /health 200 1.862ms 26d3ad13-f854-4dec-af81-ed45bad3857e
[web] 🔍 Middleware: No auth cookie found for /
⠋ Waiting for services to start...[web] 🔍 Middleware: No auth cookie found for /
⠴ Waiting for services to start...[web] 🔍 Middleware: No auth cookie found for /
[web] 🔍 Middleware: No auth cookie found for /
[web] 🔍 Middleware: No auth cookie found for /
⠧ Waiting for services to start...[web] ✨ Compilation complete
[web]  ✓ Compiled / in 3s (1452 modules)
⠇ Waiting for services to start...[web]  GET / 200 in 250ms
⠹ Waiting for services to start...[web] Performance monitoring initialized
⠸ Waiting for services to start...[web] Performance monitoring initialized
[web] Performance monitoring initialized
⠼ Waiting for services to start...[web] Performance monitoring initialized
[web] Performance monitoring initialized
⠴ Waiting for services to start...[web] Performance monitoring initialized
[web]  GET / 200 in 1040ms
⠇ Waiting for services to start...[web] 🔍 Middleware: No auth cookie found for /
[web] 🔍 Middleware: No auth cookie found for /
[web] 🔍 Middleware: No auth cookie found for /
⠏ Waiting for services to start...[web]  ✓ Compiled in 1226ms (683 modules)
⠋ Waiting for services to start...[web] Performance monitoring initialized
⠙ Waiting for services to start...[web] Performance monitoring initialized
✅ [web] Ready in 9.0s - http://localhost:3000

🚀 All services ready in 9.0s!
[hub] API: http://localhost:3001
[web] App: http://localhost:3000

Press Ctrl+C to stop all services

[web]  GET / 200 in 289ms
[web]  GET / 200 in 281ms
[web] 🔍 Middleware auth check: {
[web]   pathname: '/',
[web]   hasAuthCookie: true,
[web]   isAuthenticated: true,
[web]   hasAccessToken: true,
[web]   cookieValue: '{"state":{"user":{"id":"697b40b3-42d7-4b32-ad49-0220c2313643","email":"<EMAIL>"},"accessToke...',
[web]   authDataKeys: [ 'state', 'version' ],
[web]   authStateKeys: [ 'user', 'accessToken', 'refreshToken', 'isAuthenticated' ],
[web]   authStateIsAuthenticated: true,
[web]   authStateAccessToken: 'present'
[web] }
[web] Performance monitoring initialized
[web]  GET / 200 in 98ms
[web] 🔍 Middleware auth check: {
[web]   pathname: '/logo-small.svg',
[web]   hasAuthCookie: true,
[web]   isAuthenticated: true,
[web]   hasAccessToken: true,
[web]   cookieValue: '{"state":{"user":{"id":"697b40b3-42d7-4b32-ad49-0220c2313643","email":"<EMAIL>"},"accessToke...',
[web]   authDataKeys: [ 'state', 'version' ],
[web]   authStateKeys: [ 'user', 'accessToken', 'refreshToken', 'isAuthenticated' ],
[web]   authStateIsAuthenticated: true,
[web]   authStateAccessToken: 'present'
[web] }
[web] 🔍 Middleware: No auth cookie found for /site.webmanifest
[web] 🔍 Middleware auth check: {
[web]   pathname: '/icon-192.png',
[web]   hasAuthCookie: true,
[web]   isAuthenticated: true,
[web]   hasAccessToken: true,
[web]   cookieValue: '{"state":{"user":{"id":"697b40b3-42d7-4b32-ad49-0220c2313643","email":"<EMAIL>"},"accessToke...',
[web]   authDataKeys: [ 'state', 'version' ],
[web]   authStateKeys: [ 'user', 'accessToken', 'refreshToken', 'isAuthenticated' ],
[web]   authStateIsAuthenticated: true,
[web]   authStateAccessToken: 'present'
[web] }
[web] 🔍 Middleware auth check: {
[web]   pathname: '/icon-192.png',
[web]   hasAuthCookie: true,
[web]   isAuthenticated: true,
[web]   hasAccessToken: true,
[web]   cookieValue: '{"state":{"user":{"id":"697b40b3-42d7-4b32-ad49-0220c2313643","email":"<EMAIL>"},"accessToke...',
[web]   authDataKeys: [ 'state', 'version' ],
[web]   authStateKeys: [ 'user', 'accessToken', 'refreshToken', 'isAuthenticated' ],
[web]   authStateIsAuthenticated: true,
[web]   authStateAccessToken: 'present'
[web] }
[web] 🔍 Middleware auth check: {
[web]   pathname: '/login',
[web]   hasAuthCookie: true,
[web]   isAuthenticated: true,
[web]   hasAccessToken: true,
[web]   cookieValue: '{"state":{"user":{"id":"697b40b3-42d7-4b32-ad49-0220c2313643","email":"<EMAIL>"},"accessToke...',
[web]   authDataKeys: [ 'state', 'version' ],
[web]   authStateKeys: [ 'user', 'accessToken', 'refreshToken', 'isAuthenticated' ],
[web]   authStateIsAuthenticated: true,
[web]   authStateAccessToken: 'present'
[web] }
[web] 🏠 Redirecting to dashboard: { pathname: '/login', isAuthenticated: true, isAuthRoute: true }
[web] 🔍 Middleware auth check: {
[web]   pathname: '/dashboard',
[web]   hasAuthCookie: true,
[web]   isAuthenticated: true,
[web]   hasAccessToken: true,
[web]   cookieValue: '{"state":{"user":{"id":"697b40b3-42d7-4b32-ad49-0220c2313643","email":"<EMAIL>"},"accessToke...',
[web]   authDataKeys: [ 'state', 'version' ],
[web]   authStateKeys: [ 'user', 'accessToken', 'refreshToken', 'isAuthenticated' ],
[web]   authStateIsAuthenticated: true,
[web]   authStateAccessToken: 'present'
[web] }
[web] 🔨 Compiling pages...
[web]  ○ Compiling /dashboard ...
[web] ✨ Compilation complete
[web]  ✓ Compiled /dashboard in 2.3s (3623 modules)
[web]  GET /dashboard 200 in 2674ms
[hub] 2025-07-17T21:15:07.805Z [WARN] Authentication failed - no token provided {
[hub]   "authHeader": "missing",
[hub]   "path": "/"
[hub] }
[hub] GET /api/v1/trips?page=1&limit=20 401 1.417ms 7c4b26f2-de3f-49ff-b96f-4d50fb0e7e16
[hub] 2025-07-17T21:15:07.806Z [WARN] HTTP Request Error {
[hub]   "method": "GET",
[hub]   "url": "/?page=1&limit=20",
[hub]   "statusCode": 401,
[hub]   "responseTime": "1.417ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[web] 🔍 Middleware: No auth cookie found for /site.webmanifest
[web] 🔍 Middleware auth check: {
[web]   pathname: '/icon-192.png',
[web]   hasAuthCookie: true,
[web]   isAuthenticated: true,
[web]   hasAccessToken: true,
[web]   cookieValue: '{"state":{"user":{"id":"697b40b3-42d7-4b32-ad49-0220c2313643","email":"<EMAIL>"},"accessToke...',
[web]   authDataKeys: [ 'state', 'version' ],
[web]   authStateKeys: [ 'user', 'accessToken', 'refreshToken', 'isAuthenticated' ],
[web]   authStateIsAuthenticated: true,
[web]   authStateAccessToken: 'present'
[web] }
[web] 🔍 Middleware auth check: {
[web]   pathname: '/import',
[web]   hasAuthCookie: true,
[web]   isAuthenticated: true,
[web]   hasAccessToken: true,
[web]   cookieValue: '{"state":{"user":{"id":"697b40b3-42d7-4b32-ad49-0220c2313643","email":"<EMAIL>"},"accessToke...',
[web]   authDataKeys: [ 'state', 'version' ],
[web]   authStateKeys: [ 'user', 'accessToken', 'refreshToken', 'isAuthenticated' ],
[web]   authStateIsAuthenticated: true,
[web]   authStateAccessToken: 'present'
[web] }
[web] 🔨 Compiling pages...
[web]  ○ Compiling /import ...
[web] ✨ Compilation complete
[web]  ✓ Compiled /import in 2.1s (4903 modules)
[web]  GET /import 200 in 2233ms
[web] 🔍 Middleware: No auth cookie found for /site.webmanifest
[web] 🔍 Middleware auth check: {
[web]   pathname: '/icon-192.png',
[web]   hasAuthCookie: true,
[web]   isAuthenticated: true,
[web]   hasAccessToken: true,
[web]   cookieValue: '{"state":{"user":{"id":"697b40b3-42d7-4b32-ad49-0220c2313643","email":"<EMAIL>"},"accessToke...',
[web]   authDataKeys: [ 'state', 'version' ],
[web]   authStateKeys: [ 'user', 'accessToken', 'refreshToken', 'isAuthenticated' ],
[web]   authStateIsAuthenticated: true,
[web]   authStateAccessToken: 'present'
[web] }
[hub] 2025-07-17T21:15:15.417Z [INFO] Supabase connection pool initialized {
[hub]   "maxConnections": 50,
[hub]   "minConnections": 5
[hub] }
[hub] 2025-07-17T21:15:15.625Z [INFO] Creating AIParserService {
[hub]   "hasGeminiKey": true,
[hub]   "hasOpenRouterKey": true,
[hub]   "nodeEnv": "development"
[hub] }
[hub] 2025-07-17T21:15:15.626Z [INFO] AIParserService initialized {
[hub]   "circuitBreakerState": "CLOSED",
[hub]   "enhancedAIRouterAvailable": true
[hub] }
[hub] Warning: Indexing all PDF objects
[hub] 2025-07-17T21:15:15.855Z [INFO] PDF text extracted successfully {
[hub]   "pages": 22,
[hub]   "textLength": 27230
[hub] }
[hub] 2025-07-17T21:15:16.233Z [INFO] Redis connection pool initialized {
[hub]   "minConnections": 5,
[hub]   "totalConnections": 6
[hub] }
[hub] 2025-07-17T21:15:16.242Z [INFO] Parse session created successfully {
[hub]   "sessionId": "fded7996-2f2f-4a90-87fb-e80d35a1c073",
[hub]   "userId": "697b40b3-42d7-4b32-ad49-0220c2313643",
[hub]   "source": "gemini",
[hub]   "contentLength": 27230
[hub] }
[hub] 2025-07-17T21:15:16.298Z [INFO] [Cache] Redis connection pool cache initialized 
[hub] 2025-07-17T21:15:16.394Z [INFO] Starting background parse {
[hub]   "sessionId": "fded7996-2f2f-4a90-87fb-e80d35a1c073",
[hub]   "source": "gemini"
[hub] }
[hub] 2025-07-17T21:15:16.395Z [INFO] parseAsync started {
[hub]   "sessionId": "fded7996-2f2f-4a90-87fb-e80d35a1c073",
[hub]   "contentLength": 27230,
[hub]   "source": "gemini"
[hub] }
[hub] 2025-07-17T21:15:16.425Z [INFO] PDF import session created {
[hub]   "sessionId": "fded7996-2f2f-4a90-87fb-e80d35a1c073",
[hub]   "userId": "697b40b3-42d7-4b32-ad49-0220c2313643",
[hub]   "source": "gemini",
[hub]   "pageCount": 22
[hub] }
[hub] 2025-07-17T21:15:16.425Z [WARN] Slow API request {
[hub]   "method": "POST",
[hub]   "path": "/pdf",
[hub]   "duration": "1011.09ms",
[hub]   "queryCount": 0,
[hub]   "queryTime": "0.00ms",
[hub]   "statusCode": 200
[hub] }
[hub] 2025-07-17T21:15:16.426Z [INFO] HTTP Request {
[hub]   "method": "POST",
[hub]   "url": "/pdf",
[hub]   "statusCode": 200,
[hub]   "responseTime": "1011.494ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] POST /api/v1/import/pdf 200 1011.494ms 2bfc8c86-6184-41ae-9f7e-e42864f0db09
[hub] 2025-07-17T21:15:16.510Z [INFO] Session status updated successfully {
[hub]   "sessionId": "fded7996-2f2f-4a90-87fb-e80d35a1c073",
[hub]   "status": "processing",
[hub]   "progress": 10,
[hub]   "currentStep": "initializing"
[hub] }
[hub] 2025-07-17T21:15:16.545Z [INFO] Starting AI parse with Enhanced AI Router Service {
[hub]   "sessionId": "fded7996-2f2f-4a90-87fb-e80d35a1c073",
[hub]   "contentLength": 27230,
[hub]   "source": "gemini",
[hub]   "hasGeminiKey": true,
[hub]   "hasOpenRouterKey": true
[hub] }
[hub] 2025-07-17T21:15:16.630Z [INFO] Session status updated successfully {
[hub]   "sessionId": "fded7996-2f2f-4a90-87fb-e80d35a1c073",
[hub]   "status": "processing",
[hub]   "progress": 20,
[hub]   "currentStep": "extracting"
[hub] }
[hub] 2025-07-17T21:15:16.707Z [WARN] Slow API request {
[hub]   "method": "GET",
[hub]   "path": "/status/fded7996-2f2f-4a90-87fb-e80d35a1c073",
[hub]   "duration": "265.46ms",
[hub]   "queryCount": 0,
[hub]   "queryTime": "0.00ms",
[hub]   "statusCode": 200
[hub] }
[hub] 2025-07-17T21:15:16.708Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/fded7996-2f2f-4a90-87fb-e80d35a1c073",
[hub]   "statusCode": 200,
[hub]   "responseTime": "265.704ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/fded7996-2f2f-4a90-87fb-e80d35a1c073 200 265.704ms 4eedd8fe-07c5-4ad6-8e6b-70e6330b88b4
[hub] 2025-07-17T21:15:16.763Z [INFO] Session status updated successfully {
[hub]   "sessionId": "fded7996-2f2f-4a90-87fb-e80d35a1c073",
[hub]   "status": "processing",
[hub]   "progress": 40,
[hub]   "currentStep": "parsing"
[hub] }
[hub] 2025-07-17T21:15:16.792Z [INFO] Using Enhanced AI Router for intelligent model selection {
[hub]   "source": "gemini",
[hub]   "contentLength": 27230,
[hub]   "sessionId": "fded7996-2f2f-4a90-87fb-e80d35a1c073"
[hub] }
[hub] 2025-07-17T21:15:16.793Z [INFO] Enhanced AI Router Service: Starting parseContent {
[hub]   "contentLength": 27230,
[hub]   "source": "gemini",
[hub]   "userId": "fded7996-2f2f-4a90-87fb-e80d35a1c073",
[hub]   "timestamp": "2025-07-17T21:15:16.793Z"
[hub] }
[hub] 2025-07-17T21:15:16.793Z [INFO] Enhanced AI Router Service: Selecting optimal model...
[hub] 2025-07-17T21:15:16.794Z [INFO] Starting model selection {
[hub]   "contentLength": 27230,
[hub]   "complexity": "very_complex",
[hub]   "estimatedTokens": 14808
[hub] }
[hub] 2025-07-17T21:15:16.856Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/fded7996-2f2f-4a90-87fb-e80d35a1c073",
[hub]   "statusCode": 304,
[hub]   "responseTime": "147.186ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/fded7996-2f2f-4a90-87fb-e80d35a1c073 304 147.186ms 15d95767-40a5-4108-b415-0c10cccfbfe7
[hub] 2025-07-17T21:15:17.004Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/fded7996-2f2f-4a90-87fb-e80d35a1c073",
[hub]   "statusCode": 304,
[hub]   "responseTime": "147.251ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/fded7996-2f2f-4a90-87fb-e80d35a1c073 304 147.251ms 8d8bd974-b968-4f48-9720-a95e0bde7e5d
[hub] 2025-07-17T21:15:17.075Z [INFO] Model selected for parsing {
[hub]   "modelId": "moonshotai/kimi-k2:free",
[hub]   "provider": "moonshot",
[hub]   "reason": "Primary free tier model - under daily limit",
[hub]   "estimatedCost": 0,
[hub]   "userId": "fded7996-2f2f-4a90-87fb-e80d35a1c073"
[hub] }
[hub] 2025-07-17T21:15:17.075Z [INFO] Enhanced AI Router Service: About to execute with selected model {
[hub]   "modelId": "moonshotai/kimi-k2:free",
[hub]   "timestamp": "2025-07-17T21:15:17.075Z"
[hub] }
[hub] 2025-07-17T21:15:17.075Z [INFO] Enhanced AI Router Service: Calling executeWithModel... {
[hub]   "modelId": "moonshotai/kimi-k2:free"
[hub] }
[hub] 2025-07-17T21:15:17.075Z [INFO] Enhanced AI Router Service: executeWithModel started {
[hub]   "modelId": "moonshotai/kimi-k2:free",
[hub]   "contentLength": 27230,
[hub]   "timestamp": "2025-07-17T21:15:17.075Z"
[hub] }
[hub] 2025-07-17T21:15:17.075Z [INFO] Enhanced AI Router Service: Getting model configuration... {
[hub]   "modelId": "moonshotai/kimi-k2:free"
[hub] }
[hub] 2025-07-17T21:15:17.075Z [INFO] Enhanced AI Router Service: Prompts retrieved {
[hub]   "modelId": "moonshotai/kimi-k2:free",
[hub]   "systemPromptLength": 1188,
[hub]   "formatInstructionsLength": 117
[hub] }
[hub] 2025-07-17T21:15:17.075Z [INFO] Enhanced AI Router Service: Full prompt built {
[hub]   "modelId": "moonshotai/kimi-k2:free",
[hub]   "fullPromptLength": 28557,
[hub]   "tokenEstimate": {
[hub]     "inputTokens": 6808,
[hub]     "outputTokens": 8000,
[hub]     "complexity": "very_complex"
[hub]   }
[hub] }
[hub] 2025-07-17T21:15:17.075Z [INFO] Enhanced AI Router Service: Determining provider for model {
[hub]   "modelId": "moonshotai/kimi-k2:free"
[hub] }
[hub] 2025-07-17T21:15:17.075Z [INFO] Enhanced AI Router Service: Calling OpenRouter API for Moonshot... {
[hub]   "modelId": "moonshotai/kimi-k2:free"
[hub] }
[hub] 2025-07-17T21:15:17.076Z [INFO] Enhanced AI Router Service: callOpenRouterAPI started {
[hub]   "modelId": "moonshotai/kimi-k2:free",
[hub]   "contentLength": 27230,
[hub]   "systemPromptLength": 1188,
[hub]   "timestamp": "2025-07-17T21:15:17.076Z"
[hub] }
[hub] 2025-07-17T21:15:17.076Z [INFO] Enhanced AI Router Service: Checking API key... {
[hub]   "hasApiKey": true,
[hub]   "keyLength": 73,
[hub]   "keyPrefix": "sk-or-v1-1"
[hub] }
[hub] 2025-07-17T21:15:17.076Z [INFO] Making OpenRouter API request {
[hub]   "modelId": "moonshotai/kimi-k2:free",
[hub]   "contentLength": 27230,
[hub]   "systemPromptLength": 1188,
[hub]   "timeout": 60000
[hub] }
[hub] 2025-07-17T21:15:17.076Z [INFO] Enhanced AI Router Service: Building request payload... {
[hub]   "modelId": "moonshotai/kimi-k2:free"
[hub] }
[hub] 2025-07-17T21:15:17.076Z [INFO] Enhanced AI Router Service: Request payload built {
[hub]   "model": "moonshotai/kimi-k2:free",
[hub]   "messageCount": 2,
[hub]   "temperature": 0.3,
[hub]   "maxTokens": 4000,
[hub]   "systemMessageLength": 1188,
[hub]   "userMessageLength": 27230
[hub] }
[hub] 2025-07-17T21:15:17.076Z [INFO] Enhanced AI Router Service: About to make axios POST request... {
[hub]   "url": "https://openrouter.ai/api/v1/chat/completions",
[hub]   "timeout": 60000,
[hub]   "timestamp": "2025-07-17T21:15:17.076Z"
[hub] }
[hub] 2025-07-17T21:15:17.173Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/fded7996-2f2f-4a90-87fb-e80d35a1c073",
[hub]   "statusCode": 304,
[hub]   "responseTime": "167.671ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/fded7996-2f2f-4a90-87fb-e80d35a1c073 304 167.671ms f746687f-927a-4769-b27d-4b3fc4121fcc
[hub] 2025-07-17T21:15:17.329Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/fded7996-2f2f-4a90-87fb-e80d35a1c073",
[hub]   "statusCode": 304,
[hub]   "responseTime": "154.234ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/fded7996-2f2f-4a90-87fb-e80d35a1c073 304 154.234ms d3defeb6-eb8e-468f-885e-122dfc92f52a
[hub] 2025-07-17T21:15:17.506Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/fded7996-2f2f-4a90-87fb-e80d35a1c073",
[hub]   "statusCode": 304,
[hub]   "responseTime": "176.519ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/fded7996-2f2f-4a90-87fb-e80d35a1c073 304 176.519ms b7fff947-3441-4adc-a89d-3ce53b05da8a
[hub] 2025-07-17T21:15:17.676Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/fded7996-2f2f-4a90-87fb-e80d35a1c073",
[hub]   "statusCode": 304,
[hub]   "responseTime": "168.847ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/fded7996-2f2f-4a90-87fb-e80d35a1c073 304 168.847ms 4684642d-a06c-45c2-94b2-eb87d2f1ecd8
[hub] 2025-07-17T21:15:17.848Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/fded7996-2f2f-4a90-87fb-e80d35a1c073",
[hub]   "statusCode": 304,
[hub]   "responseTime": "171.677ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/fded7996-2f2f-4a90-87fb-e80d35a1c073 304 171.677ms 87a8c7c0-b7dd-4f3b-b1e0-2b00864d4330
[hub] 2025-07-17T21:15:18.015Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/fded7996-2f2f-4a90-87fb-e80d35a1c073",
[hub]   "statusCode": 304,
[hub]   "responseTime": "165.934ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/fded7996-2f2f-4a90-87fb-e80d35a1c073 304 165.934ms da88423a-ee9a-46b3-bd53-f019c6509d5a
[hub] 2025-07-17T21:15:18.179Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/fded7996-2f2f-4a90-87fb-e80d35a1c073",
[hub]   "statusCode": 304,
[hub]   "responseTime": "163.096ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/fded7996-2f2f-4a90-87fb-e80d35a1c073 304 163.096ms 82120cad-8a9d-444d-b61f-916ea46ffb83
[hub] 2025-07-17T21:15:18.338Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/fded7996-2f2f-4a90-87fb-e80d35a1c073",
[hub]   "statusCode": 304,
[hub]   "responseTime": "157.983ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/fded7996-2f2f-4a90-87fb-e80d35a1c073 304 157.983ms 19bc8941-0f5a-4d94-9ac4-63ad0a75476b
[hub] 2025-07-17T21:15:18.583Z [WARN] Slow API request {
[hub]   "method": "GET",
[hub]   "path": "/status/fded7996-2f2f-4a90-87fb-e80d35a1c073",
[hub]   "duration": "244.53ms",
[hub]   "queryCount": 0,
[hub]   "queryTime": "0.00ms",
[hub]   "statusCode": 200
[hub] }
[hub] 2025-07-17T21:15:18.583Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/fded7996-2f2f-4a90-87fb-e80d35a1c073",
[hub]   "statusCode": 304,
[hub]   "responseTime": "244.707ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/fded7996-2f2f-4a90-87fb-e80d35a1c073 304 244.707ms 6af1bff0-4f11-4570-a7f7-2865eaff3073
[hub] 2025-07-17T21:15:18.731Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/fded7996-2f2f-4a90-87fb-e80d35a1c073",
[hub]   "statusCode": 304,
[hub]   "responseTime": "146.457ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/fded7996-2f2f-4a90-87fb-e80d35a1c073 304 146.457ms 4fa91c7d-7a38-49d8-b852-71d99ff4650f
[hub] 2025-07-17T21:15:18.888Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/fded7996-2f2f-4a90-87fb-e80d35a1c073",
[hub]   "statusCode": 304,
[hub]   "responseTime": "156.594ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/fded7996-2f2f-4a90-87fb-e80d35a1c073 304 156.594ms 4e2f74ac-8d40-4d48-b8c3-fcdc8cd511cb
[hub] 2025-07-17T21:15:19.029Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/fded7996-2f2f-4a90-87fb-e80d35a1c073",
[hub]   "statusCode": 304,
[hub]   "responseTime": "139.65ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/fded7996-2f2f-4a90-87fb-e80d35a1c073 304 139.650ms d952e3c2-6ac1-46af-8afd-d5e9d72ddb08
[hub] 2025-07-17T21:15:19.171Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/fded7996-2f2f-4a90-87fb-e80d35a1c073",
[hub]   "statusCode": 304,
[hub]   "responseTime": "140.762ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/fded7996-2f2f-4a90-87fb-e80d35a1c073 304 140.762ms d944789f-7a4c-49aa-a5b9-1e3b98ab9f17
[hub] 2025-07-17T21:15:19.337Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/fded7996-2f2f-4a90-87fb-e80d35a1c073",
[hub]   "statusCode": 304,
[hub]   "responseTime": "165.996ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/fded7996-2f2f-4a90-87fb-e80d35a1c073 304 165.996ms f31f2162-c14e-4157-88f6-955f8e7cd730
[hub] 2025-07-17T21:15:19.515Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/fded7996-2f2f-4a90-87fb-e80d35a1c073",
[hub]   "statusCode": 304,
[hub]   "responseTime": "176.619ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/fded7996-2f2f-4a90-87fb-e80d35a1c073 304 176.619ms b1713346-1791-4ce5-8098-9542a1617a9a
[hub] 2025-07-17T21:15:19.707Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/fded7996-2f2f-4a90-87fb-e80d35a1c073",
[hub]   "statusCode": 304,
[hub]   "responseTime": "191.532ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/fded7996-2f2f-4a90-87fb-e80d35a1c073 304 191.532ms ed62d683-996f-4157-b39d-e4d5ea5f02d0
[hub] 2025-07-17T21:15:19.877Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/fded7996-2f2f-4a90-87fb-e80d35a1c073",
[hub]   "statusCode": 304,
[hub]   "responseTime": "168.738ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/fded7996-2f2f-4a90-87fb-e80d35a1c073 304 168.738ms 1f631e6d-53c3-4f2a-b4f2-eef9eadd2945
[hub] 2025-07-17T21:15:20.050Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/fded7996-2f2f-4a90-87fb-e80d35a1c073",
[hub]   "statusCode": 304,
[hub]   "responseTime": "171.76ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/fded7996-2f2f-4a90-87fb-e80d35a1c073 304 171.760ms 89379045-35a4-4003-a567-7e30f2680caa
[hub] 2025-07-17T21:15:20.210Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/fded7996-2f2f-4a90-87fb-e80d35a1c073",
[hub]   "statusCode": 304,
[hub]   "responseTime": "159.651ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/fded7996-2f2f-4a90-87fb-e80d35a1c073 304 159.651ms 8a8ca8d9-2724-4099-b162-d220131fbd8d
[hub] 2025-07-17T21:15:20.374Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/fded7996-2f2f-4a90-87fb-e80d35a1c073",
[hub]   "statusCode": 304,
[hub]   "responseTime": "163.223ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/fded7996-2f2f-4a90-87fb-e80d35a1c073 304 163.223ms d0d4e303-3101-4286-ab7b-b82ff738fbe4
[hub] 2025-07-17T21:15:20.544Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/fded7996-2f2f-4a90-87fb-e80d35a1c073",
[hub]   "statusCode": 304,
[hub]   "responseTime": "168.364ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/fded7996-2f2f-4a90-87fb-e80d35a1c073 304 168.364ms 89ce4c15-ff4e-4a99-b1bc-0c122fa64738
[hub] 2025-07-17T21:15:20.725Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/fded7996-2f2f-4a90-87fb-e80d35a1c073",
[hub]   "statusCode": 304,
[hub]   "responseTime": "179.939ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/fded7996-2f2f-4a90-87fb-e80d35a1c073 304 179.939ms 0b3a06b9-a639-4d2e-9626-1f9e9962fa56
[hub] 2025-07-17T21:15:20.879Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/fded7996-2f2f-4a90-87fb-e80d35a1c073",
[hub]   "statusCode": 304,
[hub]   "responseTime": "153.785ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/fded7996-2f2f-4a90-87fb-e80d35a1c073 304 153.785ms 01e0f5e2-3eb3-4827-9e86-49506c065d84
[hub] 2025-07-17T21:15:21.042Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/fded7996-2f2f-4a90-87fb-e80d35a1c073",
[hub]   "statusCode": 304,
[hub]   "responseTime": "162.134ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/fded7996-2f2f-4a90-87fb-e80d35a1c073 304 162.134ms a26ec516-edef-4485-b3c5-1dd560922565
[hub] 2025-07-17T21:15:21.208Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/fded7996-2f2f-4a90-87fb-e80d35a1c073",
[hub]   "statusCode": 304,
[hub]   "responseTime": "164.54ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/fded7996-2f2f-4a90-87fb-e80d35a1c073 304 164.540ms ba145230-eb65-4613-a44f-7ccdd9ae36b2
[hub] 2025-07-17T21:15:21.375Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/fded7996-2f2f-4a90-87fb-e80d35a1c073",
[hub]   "statusCode": 304,
[hub]   "responseTime": "165.983ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/fded7996-2f2f-4a90-87fb-e80d35a1c073 304 165.983ms eec69320-4189-4e09-bc76-804e7253f79c
[hub] 2025-07-17T21:15:21.547Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/fded7996-2f2f-4a90-87fb-e80d35a1c073",
[hub]   "statusCode": 304,
[hub]   "responseTime": "171.44ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/fded7996-2f2f-4a90-87fb-e80d35a1c073 304 171.440ms dec9b8f5-75df-4e35-a42a-8a4b30c413ab
[hub] 2025-07-17T21:15:21.729Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/fded7996-2f2f-4a90-87fb-e80d35a1c073",
[hub]   "statusCode": 304,
[hub]   "responseTime": "181.452ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/fded7996-2f2f-4a90-87fb-e80d35a1c073 304 181.452ms eac88814-10c7-451b-8ceb-5a4cb66e8e8f
[hub] 2025-07-17T21:15:21.885Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/fded7996-2f2f-4a90-87fb-e80d35a1c073",
[hub]   "statusCode": 304,
[hub]   "responseTime": "154.903ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/fded7996-2f2f-4a90-87fb-e80d35a1c073 304 154.903ms 750f5f52-6808-4284-8d60-9beb69c941da
[hub] 2025-07-17T21:15:22.045Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/fded7996-2f2f-4a90-87fb-e80d35a1c073",
[hub]   "statusCode": 304,
[hub]   "responseTime": "158.682ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/fded7996-2f2f-4a90-87fb-e80d35a1c073 304 158.682ms 3e66063d-954d-41ca-8c0d-04862ebcad4b
[hub] 2025-07-17T21:15:22.193Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/fded7996-2f2f-4a90-87fb-e80d35a1c073",
[hub]   "statusCode": 304,
[hub]   "responseTime": "146.926ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/fded7996-2f2f-4a90-87fb-e80d35a1c073 304 146.926ms 5c2d449f-0474-461c-a602-90c3637c4bb3
[hub] 2025-07-17T21:15:22.344Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/fded7996-2f2f-4a90-87fb-e80d35a1c073",
[hub]   "statusCode": 304,
[hub]   "responseTime": "150.568ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/fded7996-2f2f-4a90-87fb-e80d35a1c073 304 150.568ms fa63b21e-99d1-42c0-86f6-5a5aeeb0c530
[hub] 2025-07-17T21:15:22.521Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/fded7996-2f2f-4a90-87fb-e80d35a1c073",
[hub]   "statusCode": 304,
[hub]   "responseTime": "175.514ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/fded7996-2f2f-4a90-87fb-e80d35a1c073 304 175.514ms c2e92ee7-37b6-4085-9c51-5856c295bfd8
[hub] 2025-07-17T21:15:22.709Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/fded7996-2f2f-4a90-87fb-e80d35a1c073",
[hub]   "statusCode": 304,
[hub]   "responseTime": "185.819ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/fded7996-2f2f-4a90-87fb-e80d35a1c073 304 185.819ms e9c0c995-8aff-4634-9a4c-2d48fa4485f3
[hub] 2025-07-17T21:15:22.875Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/fded7996-2f2f-4a90-87fb-e80d35a1c073",
[hub]   "statusCode": 304,
[hub]   "responseTime": "164.63ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/fded7996-2f2f-4a90-87fb-e80d35a1c073 304 164.630ms be7e0a6c-6f10-43ff-aca5-9519083b730b
[hub] 2025-07-17T21:15:23.028Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/fded7996-2f2f-4a90-87fb-e80d35a1c073",
[hub]   "statusCode": 304,
[hub]   "responseTime": "152.598ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/fded7996-2f2f-4a90-87fb-e80d35a1c073 304 152.598ms 5ed14800-5866-422b-90ed-097b008c7595
[hub] 2025-07-17T21:15:23.172Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/fded7996-2f2f-4a90-87fb-e80d35a1c073",
[hub]   "statusCode": 304,
[hub]   "responseTime": "142.995ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/fded7996-2f2f-4a90-87fb-e80d35a1c073 304 142.995ms 83cb88e3-1ac0-43cc-9954-372f484ed0f6
[hub] 2025-07-17T21:15:23.336Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/fded7996-2f2f-4a90-87fb-e80d35a1c073",
[hub]   "statusCode": 304,
[hub]   "responseTime": "162.953ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/fded7996-2f2f-4a90-87fb-e80d35a1c073 304 162.953ms 7d10e5f2-d295-49b5-8800-b22cab92743d
[hub] 2025-07-17T21:15:23.498Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/fded7996-2f2f-4a90-87fb-e80d35a1c073",
[hub]   "statusCode": 304,
[hub]   "responseTime": "161.156ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/fded7996-2f2f-4a90-87fb-e80d35a1c073 304 161.156ms 1a899bf1-753e-447e-889e-01adc4c01d44
[hub] 2025-07-17T21:15:23.670Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/fded7996-2f2f-4a90-87fb-e80d35a1c073",
[hub]   "statusCode": 304,
[hub]   "responseTime": "171.302ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/fded7996-2f2f-4a90-87fb-e80d35a1c073 304 171.302ms 8d16eb39-99bb-4325-80be-175b973c08e7
[hub] 2025-07-17T21:15:23.830Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/fded7996-2f2f-4a90-87fb-e80d35a1c073",
[hub]   "statusCode": 304,
[hub]   "responseTime": "158.404ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/fded7996-2f2f-4a90-87fb-e80d35a1c073 304 158.404ms 83c08a81-8381-4d04-9d95-f65e4f4af332
[hub] 2025-07-17T21:15:23.992Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/fded7996-2f2f-4a90-87fb-e80d35a1c073",
[hub]   "statusCode": 304,
[hub]   "responseTime": "161.222ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/fded7996-2f2f-4a90-87fb-e80d35a1c073 304 161.222ms b3f8d4da-00f7-4fa9-9d5a-5090a7acd94d
[hub] 2025-07-17T21:15:24.234Z [WARN] Slow API request {
[hub]   "method": "GET",
[hub]   "path": "/status/fded7996-2f2f-4a90-87fb-e80d35a1c073",
[hub]   "duration": "241.64ms",
[hub]   "queryCount": 0,
[hub]   "queryTime": "0.00ms",
[hub]   "statusCode": 200
[hub] }
[hub] 2025-07-17T21:15:24.235Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/fded7996-2f2f-4a90-87fb-e80d35a1c073",
[hub]   "statusCode": 304,
[hub]   "responseTime": "241.886ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/fded7996-2f2f-4a90-87fb-e80d35a1c073 304 241.886ms 804eabc5-ff27-4859-9527-6f0aad15754f
[hub] 2025-07-17T21:15:24.401Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/fded7996-2f2f-4a90-87fb-e80d35a1c073",
[hub]   "statusCode": 304,
[hub]   "responseTime": "165.15ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/fded7996-2f2f-4a90-87fb-e80d35a1c073 304 165.150ms ec0e201a-2247-4522-bb4b-34de9ab79e1a
[hub] 2025-07-17T21:15:24.570Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/fded7996-2f2f-4a90-87fb-e80d35a1c073",
[hub]   "statusCode": 304,
[hub]   "responseTime": "167.869ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/fded7996-2f2f-4a90-87fb-e80d35a1c073 304 167.869ms d7242522-d7ef-4a6b-8c21-a0cfd1436fef
[hub] 2025-07-17T21:15:24.728Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/fded7996-2f2f-4a90-87fb-e80d35a1c073",
[hub]   "statusCode": 304,
[hub]   "responseTime": "157.081ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/fded7996-2f2f-4a90-87fb-e80d35a1c073 304 157.081ms 64f070d7-394b-4227-9c84-2cdc4edfc25b
[hub] 2025-07-17T21:15:24.889Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/fded7996-2f2f-4a90-87fb-e80d35a1c073",
[hub]   "statusCode": 304,
[hub]   "responseTime": "160.322ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/fded7996-2f2f-4a90-87fb-e80d35a1c073 304 160.322ms 37d0a41e-2154-443c-bebd-81e5f83b44c3
[hub] 2025-07-17T21:15:25.059Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/fded7996-2f2f-4a90-87fb-e80d35a1c073",
[hub]   "statusCode": 304,
[hub]   "responseTime": "169.011ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/fded7996-2f2f-4a90-87fb-e80d35a1c073 304 169.011ms e675df8c-44db-4c23-8b98-276edb62ba9f
[hub] 2025-07-17T21:15:25.261Z [WARN] Slow API request {
[hub]   "method": "GET",
[hub]   "path": "/status/fded7996-2f2f-4a90-87fb-e80d35a1c073",
[hub]   "duration": "200.79ms",
[hub]   "queryCount": 0,
[hub]   "queryTime": "0.00ms",
[hub]   "statusCode": 200
[hub] }
[hub] 2025-07-17T21:15:25.261Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/fded7996-2f2f-4a90-87fb-e80d35a1c073",
[hub]   "statusCode": 304,
[hub]   "responseTime": "201.032ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/fded7996-2f2f-4a90-87fb-e80d35a1c073 304 201.032ms d1483ced-9b34-4ab5-8591-1df73b36f103
[hub] 2025-07-17T21:15:25.482Z [WARN] Slow API request {
[hub]   "method": "GET",
[hub]   "path": "/status/fded7996-2f2f-4a90-87fb-e80d35a1c073",
[hub]   "duration": "220.43ms",
[hub]   "queryCount": 0,
[hub]   "queryTime": "0.00ms",
[hub]   "statusCode": 200
[hub] }
[hub] 2025-07-17T21:15:25.483Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/fded7996-2f2f-4a90-87fb-e80d35a1c073",
[hub]   "statusCode": 304,
[hub]   "responseTime": "220.646ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/fded7996-2f2f-4a90-87fb-e80d35a1c073 304 220.646ms e02ce5b9-37b6-4cad-88e6-40b82319b786


Shutting down services...
[hub] Process exited with code null
Terminate batch job (Y/N)?
PS C:\Users\<USER>\Travelviz\Travelviz> 