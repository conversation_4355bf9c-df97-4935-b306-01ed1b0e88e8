#!/usr/bin/env tsx

/**
 * Test script to verify PDF upload fixes with the actual problematic PDF
 */

import axios from 'axios';
import FormData from 'form-data';
import fs from 'fs';
import path from 'path';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: path.join(process.cwd(), '.env.local') });

const API_BASE_URL = 'http://localhost:3001';
const PDF_PATH = 'C:\\Users\\<USER>\\Travelviz\\Travelviz\\Updated 15-Day European Travel Itinerary_ London, Madrid, Lisbon and Porto.pdf';

// Test user credentials
const TEST_USER = {
  email: '<EMAIL>',
  password: 'Flaremmk123!'
};

interface TestResult {
  phase: string;
  success: boolean;
  duration: number;
  data?: any;
  error?: string;
}

async function authenticateUser(): Promise<string> {
  console.log('🔐 Authenticating user...');

  try {
    const response = await axios.post(`${API_BASE_URL}/api/v1/auth/login`, {
      email: TEST_USER.email,
      password: TEST_USER.password
    }, {
      timeout: 10000
    });

    console.log('📊 Auth response:', JSON.stringify(response.data, null, 2));

    // Check different possible response structures
    const accessToken = response.data?.data?.access_token ||
                       response.data?.data?.accessToken ||
                       response.data?.accessToken ||
                       response.data?.token;

    if (accessToken) {
      console.log('✅ Authentication successful');
      return accessToken;
    } else {
      throw new Error(`No access token in response. Response structure: ${JSON.stringify(response.data)}`);
    }
  } catch (error) {
    if (axios.isAxiosError(error)) {
      console.error('❌ Auth request failed:', {
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data
      });
    } else {
      console.error('❌ Authentication failed:', error);
    }
    throw error;
  }
}

async function uploadPDF(authToken: string): Promise<string> {
  console.log('📄 Uploading PDF...');
  
  if (!fs.existsSync(PDF_PATH)) {
    throw new Error(`PDF file not found: ${PDF_PATH}`);
  }

  const formData = new FormData();
  formData.append('file', fs.createReadStream(PDF_PATH));
  formData.append('source', 'file');

  try {
    const response = await axios.post(
      `${API_BASE_URL}/api/v1/import/pdf`,
      formData,
      {
        headers: {
          'Authorization': `Bearer ${authToken}`,
          ...formData.getHeaders()
        },
        timeout: 10000 // 10 seconds for upload
      }
    );

    console.log('📊 Upload response:', JSON.stringify(response.data, null, 2));

    // Check for sessionId (PDF endpoint returns sessionId, not importId)
    const sessionId = response.data?.data?.sessionId || response.data?.data?.importId;

    if (sessionId) {
      console.log('✅ PDF uploaded successfully');
      console.log(`📋 Session ID: ${sessionId}`);
      return sessionId;
    } else {
      throw new Error(`No session ID in response. Response: ${JSON.stringify(response.data)}`);
    }
  } catch (error) {
    if (axios.isAxiosError(error)) {
      console.error('❌ PDF upload failed:', {
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data
      });
    } else {
      console.error('❌ PDF upload failed:', error);
    }
    throw error;
  }
}

async function pollStatus(importId: string, authToken: string): Promise<any> {
  console.log('🔄 Starting status polling...');
  console.log(`📊 Import ID: ${importId}`);
  
  const startTime = Date.now();
  const maxPollingTime = 120000; // 2 minutes
  const pollingInterval = 5000; // 5 seconds (our intended interval)
  
  let attempts = 0;
  let lastStatus = '';
  let lastProgress = -1;
  
  while (Date.now() - startTime < maxPollingTime) {
    attempts++;
    const pollStartTime = Date.now();
    
    try {
      console.log(`[${new Date().toISOString()}] Poll attempt ${attempts}`);
      
      const response = await axios.get(
        `${API_BASE_URL}/api/v1/import/status/${importId}`,
        {
          headers: {
            'Authorization': `Bearer ${authToken}`
          },
          timeout: 10000
        }
      );

      const { status, progress, currentStep, result, error } = response.data.data || {};
      const pollDuration = Date.now() - pollStartTime;
      
      console.log(`📊 Status: ${status}, Progress: ${progress}%, Step: ${currentStep} (${pollDuration}ms)`);
      
      // Track progress changes
      if (status !== lastStatus || progress !== lastProgress) {
        console.log(`🔄 Status changed: ${lastStatus} → ${status}, Progress: ${lastProgress}% → ${progress}%`);
        lastStatus = status;
        lastProgress = progress;
      }

      // Handle completion
      if (status === 'completed') {
        const totalDuration = Date.now() - startTime;
        console.log(`✅ Import completed successfully in ${Math.round(totalDuration / 1000)}s`);
        console.log(`📊 Total polling attempts: ${attempts}`);
        console.log(`📊 Average poll interval: ${Math.round(totalDuration / attempts)}ms`);
        
        if (result) {
          console.log(`📋 Result preview: ${JSON.stringify(result).substring(0, 200)}...`);
        }
        
        return result;
      }

      // Handle errors
      if (status === 'failed' || status === 'error') {
        const totalDuration = Date.now() - startTime;
        console.log(`❌ Import failed after ${Math.round(totalDuration / 1000)}s`);
        console.log(`📊 Total polling attempts: ${attempts}`);
        throw new Error(`Import failed: ${error || 'Unknown error'}`);
      }

    } catch (pollError) {
      console.error(`⚠️  Poll attempt ${attempts} failed:`, pollError.message);
      
      if (axios.isAxiosError(pollError)) {
        if (pollError.code === 'ECONNABORTED') {
          console.log('⏰ Poll request timed out');
        } else if (pollError.response?.status === 401) {
          throw new Error('Authentication expired');
        }
      }
    }

    // Wait before next poll
    console.log(`⏳ Waiting ${pollingInterval}ms before next poll...`);
    await new Promise(resolve => setTimeout(resolve, pollingInterval));
  }

  // Timeout reached
  const totalDuration = Date.now() - startTime;
  console.log(`⏰ Polling timed out after ${Math.round(totalDuration / 1000)}s`);
  console.log(`📊 Total polling attempts: ${attempts}`);
  throw new Error('Polling timeout - import taking too long');
}

async function runTest(): Promise<void> {
  console.log('🚀 Starting PDF Upload Test with Fixes...\n');
  console.log(`📄 PDF File: ${PDF_PATH}`);
  console.log(`🌐 API Base URL: ${API_BASE_URL}\n`);

  const results: TestResult[] = [];
  let authToken: string;
  let importId: string;

  try {
    // Phase 1: Authentication
    const authStart = Date.now();
    authToken = await authenticateUser();
    results.push({
      phase: 'Authentication',
      success: true,
      duration: Date.now() - authStart
    });

    // Phase 2: PDF Upload
    const uploadStart = Date.now();
    importId = await uploadPDF(authToken);
    results.push({
      phase: 'PDF Upload',
      success: true,
      duration: Date.now() - uploadStart,
      data: { importId }
    });

    // Phase 3: Status Polling
    const pollingStart = Date.now();
    const result = await pollStatus(importId, authToken);
    results.push({
      phase: 'Status Polling',
      success: true,
      duration: Date.now() - pollingStart,
      data: result
    });

    // Success summary
    console.log('\n🎉 TEST COMPLETED SUCCESSFULLY!');
    console.log('='.repeat(50));
    
    results.forEach(result => {
      console.log(`✅ ${result.phase}: ${Math.round(result.duration / 1000)}s`);
    });

    const totalDuration = results.reduce((sum, r) => sum + r.duration, 0);
    console.log(`⏱️  Total test duration: ${Math.round(totalDuration / 1000)}s`);

  } catch (error) {
    console.error('\n❌ TEST FAILED!');
    console.error('='.repeat(50));
    console.error('Error:', error instanceof Error ? error.message : String(error));
    
    // Show partial results
    if (results.length > 0) {
      console.log('\n📊 Partial Results:');
      results.forEach(result => {
        const status = result.success ? '✅' : '❌';
        console.log(`${status} ${result.phase}: ${Math.round(result.duration / 1000)}s`);
      });
    }
    
    process.exit(1);
  }
}

// Run the test
runTest().catch(console.error);
